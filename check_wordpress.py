#!/usr/bin/env python3
"""
WordPress Credentials Checker

This script helps users verify their WordPress credentials and API access.
"""

import sys
import requests
import argparse
import getpass

def check_wordpress_credentials(url, username, password):
    """Check if WordPress credentials are valid"""
    # Normalize URL
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Remove trailing slash if present
    if url.endswith('/'):
        url = url[:-1]
    
    # Create API URL
    api_url = f"{url}/wp-json/wp/v2/users/me"
    
    try:
        # Test authentication
        response = requests.get(
            api_url,
            auth=(username, password)
        )
        
        if response.status_code == 200:
            user_data = response.json()
            print("\n✅ Authentication successful!")
            print(f"Logged in as: {user_data.get('name', 'Unknown')} ({user_data.get('slug', 'Unknown')})")
            print(f"User role(s): {', '.join(user_data.get('roles', ['Unknown']))}")
            
            # Check if user has necessary permissions
            if 'administrator' in user_data.get('roles', []) or 'editor' in user_data.get('roles', []):
                print("✅ User has sufficient permissions to publish posts")
            else:
                print("⚠️ Warning: User may not have sufficient permissions to publish posts")
                print("   Recommended roles: administrator or editor")
            
            return True
        else:
            print(f"\n❌ Authentication failed: {response.status_code} {response.reason}")
            if response.status_code == 401:
                print("   Invalid username or password")
            elif response.status_code == 403:
                print("   User does not have permission to access the API")
            return False
    
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Connection error: Could not connect to {url}")
        print("   Please check if the URL is correct and the site is accessible")
        return False
    
    except requests.exceptions.RequestException as e:
        print(f"\n❌ Request error: {str(e)}")
        return False

def check_wordpress_api(url):
    """Check if WordPress REST API is accessible"""
    # Normalize URL
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Remove trailing slash if present
    if url.endswith('/'):
        url = url[:-1]
    
    # Create API URL
    api_url = f"{url}/wp-json"
    
    try:
        # Test API access
        response = requests.get(api_url)
        
        if response.status_code == 200:
            print("\n✅ WordPress REST API is accessible")
            
            # Check if the API includes wp/v2 namespace
            if 'wp/v2' in response.text:
                print("✅ WordPress REST API v2 is available")
            else:
                print("⚠️ Warning: WordPress REST API v2 namespace not found")
            
            return True
        else:
            print(f"\n❌ WordPress REST API check failed: {response.status_code} {response.reason}")
            return False
    
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Connection error: Could not connect to {url}")
        print("   Please check if the URL is correct and the site is accessible")
        return False
    
    except requests.exceptions.RequestException as e:
        print(f"\n❌ Request error: {str(e)}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Check WordPress credentials and API access')
    parser.add_argument('--url', help='WordPress site URL')
    parser.add_argument('--username', help='WordPress username')
    parser.add_argument('--password', help='WordPress password')
    
    args = parser.parse_args()
    
    print("\n" + "="*60)
    print("WordPress Credentials and API Checker")
    print("="*60)
    
    # Get URL
    url = args.url
    if not url:
        url = input("\nEnter WordPress site URL (e.g., example.com): ")
    
    # Check WordPress API
    api_accessible = check_wordpress_api(url)
    
    if not api_accessible:
        print("\n⚠️ WordPress REST API is not accessible. Authentication check may fail.")
        proceed = input("Do you want to proceed with authentication check anyway? (y/n): ")
        if proceed.lower() != 'y':
            print("\nExiting...")
            return
    
    # Get username
    username = args.username
    if not username:
        username = input("\nEnter WordPress username: ")
    
    # Get password
    password = args.password
    if not password:
        password = getpass.getpass("Enter WordPress password: ")
    
    # Check credentials
    credentials_valid = check_wordpress_credentials(url, username, password)
    
    print("\n" + "="*60)
    if credentials_valid:
        print("WordPress credentials are valid and can be used with the EV Blog Automation Suite")
    else:
        print("WordPress credentials check failed")
        print("Please verify your credentials and try again")
    print("="*60 + "\n")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nAn error occurred: {str(e)}")
        sys.exit(1)
