import requests
import logging
import os
import mimetypes
from config.config import <PERSON><PERSON><PERSON><PERSON><PERSON>_URL as DEFAULT_WORDPRESS_URL
from config.config import WORDPRESS_USERNAME as DEFAULT_WORDPRESS_USERNAME
from config.config import WOR<PERSON><PERSON><PERSON>_PASSWORD as DEFAULT_WORDPRESS_PASSWORD

class WordPressIntegration:
    def __init__(self, wordpress_url=None, wordpress_username=None, wordpress_password=None):
        self.setup_logging()

        # Use provided values or fall back to defaults
        self.wordpress_url = (wordpress_url or DEFAULT_WORDPRESS_URL).rstrip('/')
        self.wordpress_username = wordpress_username or DEFAULT_WORDPRESS_USERNAME
        self.wordpress_password = wordpress_password or DEFAULT_WORDPRESS_PASSWORD

        # Validate WordPress URL
        if not self.wordpress_url:
            self.logger.error("No WordPress URL provided")
            raise ValueError("No WordPress URL provided. Please enter a valid WordPress URL.")

        # Ensure URL has a scheme
        if not self.wordpress_url.startswith(('http://', 'https://')):
            self.logger.error(f"Invalid WordPress URL (missing http:// or https://): {self.wordpress_url}")
            raise ValueError(f"Invalid WordPress URL: {self.wordpress_url}. URL must start with http:// or https://")

        # Set up API endpoints
        self.base_url = f"{self.wordpress_url}/wp-json/wp/v2"
        self.media_base_url = f"{self.wordpress_url}/wp-content/uploads"
        self.auth = (self.wordpress_username, self.wordpress_password)

        self.logger.info(f"Initialized WordPress integration for {self.wordpress_url}")

    def setup_logging(self):
        self.logger = logging.getLogger(__name__)

    def get_mime_type(self, file_path):
        """Get the MIME type of a file"""
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'image/jpeg'

    def upload_media(self, image_path):
        """Upload an image to WordPress media library"""
        try:
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")

            mime_type = self.get_mime_type(image_path)
            filename = os.path.basename(image_path)

            with open(image_path, 'rb') as image_file:
                files = {
                    'file': (filename, image_file, mime_type)
                }
                headers = {
                    'Content-Disposition': f'attachment; filename="{filename}"'
                }

                response = requests.post(
                    f"{self.base_url}/media",
                    auth=self.auth,
                    files=files,
                    headers=headers
                )

                response.raise_for_status()
                media_data = response.json()

                if 'id' not in media_data or 'source_url' not in media_data:
                    raise ValueError("No media ID or source URL in WordPress response")

                media_id = media_data['id']
                image_url = media_data['source_url']
                self.logger.info(f"Successfully uploaded image: {filename} -> ID: {media_id}, URL: {image_url}")
                return {'id': media_id, 'url': image_url}

        except Exception as e:
            self.logger.error(f"Error uploading image {image_path}: {str(e)}")
            raise

    def get_or_create_tag(self, tag_name):
        """Get a tag ID by name or create it if it doesn't exist"""
        try:
            # First, try to find the tag by name
            response = requests.get(
                f"{self.base_url}/tags",
                auth=self.auth,
                params={'search': tag_name}
            )
            response.raise_for_status()
            tags = response.json()

            # Check if we found an exact match
            for tag in tags:
                if tag['name'].lower() == tag_name.lower():
                    self.logger.info(f"Found existing tag: {tag_name} (ID: {tag['id']})")
                    return tag['id']

            # If not found, create a new tag
            response = requests.post(
                f"{self.base_url}/tags",
                auth=self.auth,
                json={'name': tag_name}
            )
            response.raise_for_status()
            tag_id = response.json()['id']
            self.logger.info(f"Created new tag: {tag_name} (ID: {tag_id})")
            return tag_id
        except Exception as e:
            self.logger.error(f"Error getting/creating tag '{tag_name}': {str(e)}")
            return None

    def get_or_create_category(self, category_name):
        """Get a category ID by name or create it if it doesn't exist"""
        try:
            # First, try to find the category by name
            response = requests.get(
                f"{self.base_url}/categories",
                auth=self.auth,
                params={'search': category_name}
            )
            response.raise_for_status()
            categories = response.json()

            # Check if we found an exact match
            for category in categories:
                if category['name'].lower() == category_name.lower():
                    self.logger.info(f"Found existing category: {category_name} (ID: {category['id']})")
                    return category['id']

            # If not found, create a new category
            response = requests.post(
                f"{self.base_url}/categories",
                auth=self.auth,
                json={'name': category_name}
            )
            response.raise_for_status()
            category_id = response.json()['id']
            self.logger.info(f"Created new category: {category_name} (ID: {category_id})")
            return category_id
        except Exception as e:
            self.logger.error(f"Error getting/creating category '{category_name}': {str(e)}")
            return None

    def create_post(self, title, content, featured_media=None, tags=None, categories=None, status='publish'):
        """Create a new blog post with optional featured image, tags, and categories"""
        try:
            post_data = {
                'title': title,
                'content': content,
                'status': status
            }

            if featured_media:
                # featured_media should be the media ID
                post_data['featured_media'] = int(featured_media)

            # Process tags
            if tags and isinstance(tags, list) and len(tags) > 0:
                tag_ids = []
                for tag_name in tags:
                    tag_id = self.get_or_create_tag(tag_name.strip())
                    if tag_id:
                        tag_ids.append(tag_id)

                if tag_ids:
                    post_data['tags'] = tag_ids
                    self.logger.info(f"Adding tags to post: {', '.join(tags)}")

            # Process categories
            if categories and isinstance(categories, list) and len(categories) > 0:
                category_ids = []
                for category_name in categories:
                    category_id = self.get_or_create_category(category_name.strip())
                    if category_id:
                        category_ids.append(category_id)

                if category_ids:
                    post_data['categories'] = category_ids
                    self.logger.info(f"Adding categories to post: {', '.join(categories)}")

            response = requests.post(
                f"{self.base_url}/posts",
                auth=self.auth,
                json=post_data
            )
            response.raise_for_status()
            post_id = response.json()['id']
            self.logger.info(f"Successfully created post with ID: {post_id}")
            return post_id
        except Exception as e:
            self.logger.error(f"Error creating post: {str(e)}")
            raise

    def publish_post(self, title, content, featured_image_path=None, tags=None, categories=None):
        """Publish a blog post with optional featured image, tags, and categories"""
        try:
            featured_media_id = None
            if featured_image_path:
                media_data = self.upload_media(featured_image_path)
                featured_media_id = media_data['id']

            # Process tags and categories
            tag_list = []
            if tags:
                if isinstance(tags, str):
                    # Split by comma and clean
                    tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
                    # Limit to 9 tags
                    tag_list = tag_list[:9]
                elif isinstance(tags, list):
                    tag_list = [tag.strip() for tag in tags if tag.strip()][:9]

            category_list = []
            if categories:
                if isinstance(categories, str):
                    # Split by comma and clean
                    category_list = [cat.strip() for cat in categories.split(',') if cat.strip()]
                    # Limit to 3 categories
                    category_list = category_list[:3]
                elif isinstance(categories, list):
                    category_list = [cat.strip() for cat in categories if cat.strip()][:3]

            # Create and publish the post
            post_id = self.create_post(
                title=title,
                content=content,
                featured_media=featured_media_id,
                tags=tag_list,
                categories=category_list
            )

            self.logger.info(f"Successfully published post with ID: {post_id}")
            return post_id
        except Exception as e:
            self.logger.error(f"Error publishing post: {str(e)}")
            raise