import logging
import re
from config.config import MAX_TOKENS

class ManagerAgent:
    """
    Manager Agent for handling longer blog post generation.
    This agent is responsible for:
    1. Creating article structure
    2. Splitting the article into sections
    3. Coordinating content generation for each section
    4. Combining the sections into a complete article
    """

    def __init__(self, llm_integration):
        """
        Initialize the Manager Agent.

        Args:
            llm_integration: An instance of LLMIntegration for content generation
        """
        self.setup_logging()
        self.llm = llm_integration
        self.logger.info("ManagerAgent initialized")

    def setup_logging(self):
        """Setup logging configuration"""
        self.logger = logging.getLogger(__name__)

    def generate_article_structure(self, title, topic, keywords, context, word_count, elements_instruction=""):
        """
        Generate the structure for a long-form article.

        Args:
            title: The title of the article
            topic: The main topic of the article
            keywords: Keywords to include in the article
            context: Additional context for the article
            word_count: Target word count for the article

        Returns:
            A dictionary containing the article structure with sections
        """
        self.logger.info(f"Generating article structure for: {title} (Target: {word_count} words)")

        # Clean inputs
        title = self.llm.clean_text(title)
        topic = self.llm.clean_text(topic)
        keywords = self.llm.clean_text(keywords)
        context = self.llm.clean_text(context)

        # Calculate number of sections based on word count
        # For longer articles, we need more sections
        num_sections = max(3, min(8, int(word_count / 500)))

        # Generate structure using LLM
        elements_requirement = f"\n6. {elements_instruction}" if elements_instruction else ""

        structure_prompt = f"""You are a professional content writer specializing in electric vehicles.
Create a detailed outline for a {word_count}-word blog post with the following specifications:

Title: {title}
Main Topic: {topic}
Keywords to include: {keywords}
Context: {context}

Requirements:
1. Create an outline with exactly {num_sections} main sections (including introduction and conclusion)
2. For each section, provide a clear heading and a brief description (1-2 sentences) of what should be covered
3. Distribute the content evenly, with approximately {int(word_count / num_sections)} words per section
4. Ensure the structure flows logically from introduction to conclusion
5. Include all important keywords naturally throughout the outline{elements_requirement}

Format the response as a JSON-like structure with the following format:
```
{{
  "title": "The full title of the article",
  "introduction": {{
    "heading": "Introduction",
    "description": "Brief description of what to cover in the introduction",
    "target_words": 300
  }},
  "sections": [
    {{
      "heading": "First Section Heading",
      "description": "Brief description of what to cover in this section",
      "target_words": 500
    }},
    // More sections...
  ],
  "conclusion": {{
    "heading": "Conclusion",
    "description": "Brief description of what to cover in the conclusion",
    "target_words": 300
  }}
}}
```

Only return the JSON structure, nothing else."""

        try:
            # Get structure from LLM with increased max tokens for complex structures
            structure_text = self.llm.generate_raw_content(
                structure_prompt,
                max_tokens=MAX_TOKENS
            )

            # Extract JSON from the response
            json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', structure_text, re.DOTALL)
            if json_match:
                structure_text = json_match.group(1)

            # Clean up the structure text
            structure_text = structure_text.strip()
            if structure_text.startswith('```') and structure_text.endswith('```'):
                structure_text = structure_text[3:-3].strip()

            # Convert to Python dictionary
            import json
            structure = json.loads(structure_text)

            # Validate and fix structure if needed
            structure = self._validate_structure(structure, title, topic, num_sections, word_count)

            self.logger.info(f"Successfully generated article structure with {len(structure.get('sections', [])) + 2} sections")
            return structure

        except Exception as e:
            self.logger.error(f"Error generating article structure: {str(e)}")
            # Create a fallback structure
            return self._create_fallback_structure(title, topic, num_sections, word_count)

    def _validate_structure(self, structure, title, topic, num_sections, word_count):
        """Validate and fix the article structure if needed"""
        section_word_count = int(word_count / num_sections)

        # Ensure structure has all required fields
        if 'title' not in structure:
            structure['title'] = title

        # Validate introduction
        if 'introduction' not in structure:
            structure['introduction'] = {
                "heading": "Introduction",
                "description": f"Introduction to {topic}",
                "target_words": section_word_count
            }
        else:
            # Ensure introduction has all required fields
            intro = structure['introduction']
            if 'heading' not in intro:
                intro['heading'] = "Introduction"
            if 'description' not in intro:
                intro['description'] = f"Introduction to {topic}"
            if 'target_words' not in intro:
                intro['target_words'] = section_word_count
            # Ensure target_words is an integer
            try:
                intro['target_words'] = int(intro['target_words'])
            except (ValueError, TypeError):
                intro['target_words'] = section_word_count

        # Validate sections
        if 'sections' not in structure or not structure['sections']:
            structure['sections'] = []
            for i in range(1, num_sections - 1):
                structure['sections'].append({
                    "heading": f"Section {i}",
                    "description": f"Content about {topic} - part {i}",
                    "target_words": section_word_count
                })
        else:
            # Ensure each section has all required fields
            for i, section in enumerate(structure['sections']):
                if 'heading' not in section:
                    section['heading'] = f"Section {i+1}"
                if 'description' not in section:
                    section['description'] = f"Content about {topic} - part {i+1}"
                if 'target_words' not in section:
                    section['target_words'] = section_word_count
                # Ensure target_words is an integer
                try:
                    section['target_words'] = int(section['target_words'])
                except (ValueError, TypeError):
                    section['target_words'] = section_word_count

        # Validate conclusion
        if 'conclusion' not in structure:
            structure['conclusion'] = {
                "heading": "Conclusion",
                "description": f"Conclusion about {topic}",
                "target_words": section_word_count
            }
        else:
            # Ensure conclusion has all required fields
            concl = structure['conclusion']
            if 'heading' not in concl:
                concl['heading'] = "Conclusion"
            if 'description' not in concl:
                concl['description'] = f"Conclusion about {topic}"
            if 'target_words' not in concl:
                concl['target_words'] = section_word_count
            # Ensure target_words is an integer
            try:
                concl['target_words'] = int(concl['target_words'])
            except (ValueError, TypeError):
                concl['target_words'] = section_word_count

        return structure

    def _create_fallback_structure(self, title, topic, num_sections, word_count):
        """Create a fallback structure if the LLM fails"""
        section_word_count = int(word_count / num_sections)

        structure = {
            "title": title,
            "introduction": {
                "heading": "Introduction",
                "description": f"Introduction to {topic}",
                "target_words": section_word_count
            },
            "sections": [],
            "conclusion": {
                "heading": "Conclusion",
                "description": f"Conclusion about {topic}",
                "target_words": section_word_count
            }
        }

        # Create generic sections
        for i in range(1, num_sections - 1):
            structure["sections"].append({
                "heading": f"Section {i}",
                "description": f"Content about {topic} - part {i}",
                "target_words": section_word_count
            })

        return structure

    def generate_section_content(self, title, topic, keywords, context, section_info):
        """
        Generate content for a specific section of the article.

        Args:
            title: The title of the article
            topic: The main topic of the article
            keywords: Keywords to include in the article
            context: Additional context for the article
            section_info: Dictionary containing section information

        Returns:
            Markdown content for the section
        """
        self.logger.info(f"Generating content for section: {section_info['heading']}")

        # Clean inputs
        title = self.llm.clean_text(title)
        topic = self.llm.clean_text(topic)
        keywords = self.llm.clean_text(keywords)
        context = self.llm.clean_text(context)

        # Ensure section_info has all required fields with defaults if missing
        heading = section_info.get('heading', 'Section')
        description = section_info.get('description', f'Content about {topic}')

        # Default target words to 400 if missing or not a number
        try:
            target_words = int(section_info.get('target_words', 400))
        except (ValueError, TypeError):
            target_words = 400
            self.logger.warning(f"Invalid target_words value in section '{heading}', using default: 400")

        # Generate content using LLM
        section_prompt = f"""You are a professional content writer specializing in electric vehicles.
Write a detailed section for a blog post with the following specifications:

Article Title: {title}
Main Topic: {topic}
Keywords to include: {keywords}
Context: {context}

Section Information:
- Heading: {heading}
- Description: {description}
- Target Word Count: {target_words} words

Requirements:
1. Write ONLY this specific section, not the entire article
2. Start with the section heading as a markdown heading (e.g., ## {heading})
3. Write detailed, informative content following the section description
4. Naturally incorporate relevant keywords
5. Maintain a professional, engaging tone suitable for an electric vehicle industry website
6. Keep the content focused on this section only
7. Aim for approximately {target_words} words

Format the response in markdown with appropriate headings, bullet points, and paragraphs as needed."""

        try:
            # Get content from LLM with appropriate max tokens for the section
            # Use a token limit proportional to the target word count
            tokens_needed = min(MAX_TOKENS, target_words * 2)
            section_content = self.llm.generate_raw_content(
                section_prompt,
                max_tokens=tokens_needed
            )
            self.logger.info(f"Successfully generated content for section: {heading}")
            return section_content
        except Exception as e:
            self.logger.error(f"Error generating section content: {str(e)}")
            # Create fallback content
            return f"## {heading}\n\nContent for this section could not be generated. Please try again."

    def generate_long_article(self, title, topic, keywords, context, word_count, must_have_elements=None):
        """
        Generate a complete long-form article by creating a structure and generating content for each section.

        Args:
            title: The title of the article
            topic: The main topic of the article
            keywords: Keywords to include in the article
            context: Additional context for the article
            word_count: Target word count for the article

        Returns:
            Complete markdown content for the article
        """
        self.logger.info(f"Generating long article: {title} (Target: {word_count} words)")

        # Process must-have elements
        elements_instruction = ""
        if must_have_elements:
            elements_list = [elem.strip() for elem in must_have_elements if elem.strip()]
            if elements_list:
                elements_instruction = f"Must include these elements: {', '.join(elements_list)}"
                self.logger.info(f"Must-have elements specified: {elements_list}")

        # Generate article structure
        structure = self.generate_article_structure(title, topic, keywords, context, word_count, elements_instruction)

        # Initialize article content (without title, as WordPress will add it)
        article_content = []

        # Generate introduction
        intro_content = self.generate_section_content(
            title, topic, keywords, context, structure['introduction']
        )
        article_content.append(intro_content)

        # Generate each section
        for section in structure['sections']:
            section_content = self.generate_section_content(
                title, topic, keywords, context, section
            )
            article_content.append(section_content)

        # Generate conclusion
        conclusion_content = self.generate_section_content(
            title, topic, keywords, context, structure['conclusion']
        )
        article_content.append(conclusion_content)

        # Combine all sections
        full_article = "\n\n".join(article_content)

        self.logger.info(f"Successfully generated complete article: {title}")
        return full_article
