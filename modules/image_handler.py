import os
import time
import logging
import requests
import numpy as np
from PIL import Image, ImageStat, ImageEnhance, ImageFilter
from io import BytesIO
from config.config import (
    DEFAULT_IMAGE_PATH,
    IMAGE_DOWNLOAD_PATH
)
import sys
# Add the parent directory of the current file to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .GoogleImageScraper import GoogleImageScraper

class ImageHandler:
    def __init__(self, temp_dir=IMAGE_DOWNLOAD_PATH):
        self.temp_dir = temp_dir
        self.default_dir = DEFAULT_IMAGE_PATH
        self.logger = logging.getLogger(__name__)
        os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(DEFAULT_IMAGE_PATH, exist_ok=True)

        # Initialize Google Image Scraper
        self.webdriver_path = os.path.join(os.path.dirname(__file__), 'webdriver', 'chromedriver')

        # Check if ChromeDriver exists
        if not os.path.exists(self.webdriver_path):
            self.logger.error("ChromeDriver not found at: %s", self.webdriver_path)
            self.webdriver_path = None

    def search_google_images(self, search_query, num_images=5):
        """Search images using Google Image Scraper"""
        if not self.webdriver_path:
            self.logger.warning("ChromeDriver not available, skipping Google Images search")
            return []

        try:
            # Create a new scraper instance for each search
            google_scraper = GoogleImageScraper(
                webdriver_path=self.webdriver_path,
                image_path=self.temp_dir,
                search_key=search_query,
                number_of_images=num_images,
                headless=True,
                min_resolution=(0, 0),  # Accept any resolution
                max_resolution=(3840, 2160)  # Up to 4K resolution
            )

            image_urls = google_scraper.find_image_urls()
            if not image_urls:
                return []

            # Save images and get their paths
            google_scraper.save_images(image_urls, keep_filenames=False)

            # Get the saved image paths
            search_dir = os.path.join(self.temp_dir, search_query)
            if not os.path.exists(search_dir):
                return []

            # Accept all image files
            return [os.path.join(search_dir, f)
                   for f in os.listdir(search_dir)
                   if os.path.isfile(os.path.join(search_dir, f))]

        except Exception as e:
            self.logger.error(f"Error in Google image search: {str(e)}")
            return []

    def search_and_download_images(self, topic, keywords, num_images=5, min_quality_score=60):
        """
        Search and download high-quality images using Google Image Scraper

        Args:
            topic (str): Main topic for image search
            keywords (str): Additional keywords for image search
            num_images (int): Number of images to search for
            min_quality_score (float): Minimum quality score (0-100) to accept

        Returns:
            list: List of paths to high-quality images
        """
        try:
            search_query = f"{topic} {keywords}"
            self.logger.info(f"Searching for images with query: {search_query}")

            # Search for more images than needed to allow for quality filtering
            # We'll search for 2x the requested number to have a good pool to filter from
            search_count = num_images * 2
            self.logger.info(f"Searching for {search_count} images to find {num_images} high-quality ones")

            # Search for images using Google Image Scraper
            all_image_paths = self.search_google_images(search_query, search_count)

            if not all_image_paths:
                self.logger.warning("No images found from Google Images")
                return []

            self.logger.info(f"Found {len(all_image_paths)} images, filtering for quality...")

            # Filter images based on quality
            quality_images = self.filter_quality_images(all_image_paths, min_quality_score)

            # Limit to the requested number of images
            final_images = quality_images[:num_images]

            self.logger.info(f"Selected {len(final_images)} high-quality images out of {len(all_image_paths)} total")

            return final_images

        except Exception as e:
            self.logger.error(f"Error in image search: {str(e)}")
            return []

    def assess_image_quality(self, image_path, min_width=500, min_height=500, min_aspect_ratio=0.5, max_aspect_ratio=2.0):
        """
        Assess the quality of an image based on multiple factors

        Args:
            image_path (str): Path to the image file
            min_width (int): Minimum acceptable width in pixels
            min_height (int): Minimum acceptable height in pixels
            min_aspect_ratio (float): Minimum acceptable aspect ratio (width/height)
            max_aspect_ratio (float): Maximum acceptable aspect ratio (width/height)

        Returns:
            tuple: (quality_score, details_dict) where quality_score is a float between 0-100
                  and details_dict contains individual quality metrics
        """
        try:
            # Open the image
            img = Image.open(image_path)

            # Get basic image properties
            width, height = img.size
            aspect_ratio = width / height if height > 0 else 0

            # Convert to grayscale for some calculations
            gray_img = img.convert('L')

            # Calculate sharpness (using variance of Laplacian)
            sharpness = self._calculate_sharpness(gray_img)

            # Calculate brightness and contrast
            brightness = self._calculate_brightness(img)
            contrast = self._calculate_contrast(gray_img)

            # Calculate color diversity (for non-grayscale images)
            color_score = self._calculate_color_diversity(img)

            # Check if image meets minimum resolution requirements
            resolution_score = 0
            if width >= min_width and height >= min_height:
                # Higher resolution gets higher score, up to a point
                resolution_score = min(100, (width * height) / (1920 * 1080) * 100)

            # Check if aspect ratio is within acceptable range
            aspect_ratio_score = 0
            if min_aspect_ratio <= aspect_ratio <= max_aspect_ratio:
                # Prefer aspect ratios closer to 1 (square) or 16:9 (1.78)
                if 0.9 <= aspect_ratio <= 1.1:  # Near square
                    aspect_ratio_score = 100
                elif 1.7 <= aspect_ratio <= 1.8:  # Near 16:9
                    aspect_ratio_score = 95
                else:
                    aspect_ratio_score = 80

            # Calculate overall quality score (weighted average)
            quality_score = (
                resolution_score * 0.25 +  # Resolution is important
                sharpness * 0.25 +         # Sharpness is important
                brightness * 0.15 +        # Brightness matters
                contrast * 0.15 +          # Contrast matters
                color_score * 0.1 +        # Color diversity
                aspect_ratio_score * 0.1   # Aspect ratio
            )

            # Ensure score is between 0-100
            quality_score = max(0, min(100, quality_score))

            # Create details dictionary
            details = {
                'resolution': (width, height),
                'resolution_score': resolution_score,
                'aspect_ratio': aspect_ratio,
                'aspect_ratio_score': aspect_ratio_score,
                'sharpness': sharpness,
                'brightness': brightness,
                'contrast': contrast,
                'color_score': color_score
            }

            self.logger.info(f"Image quality assessment for {os.path.basename(image_path)}: {quality_score:.2f}/100")

            return quality_score, details

        except Exception as e:
            self.logger.error(f"Error assessing image quality for {image_path}: {str(e)}")
            return 0, {'error': str(e)}

    def _calculate_sharpness(self, gray_img):
        """Calculate image sharpness using variance of Laplacian method"""
        try:
            # Apply Laplacian filter
            laplacian_img = gray_img.filter(ImageFilter.FIND_EDGES)

            # Calculate variance of the Laplacian
            stat = ImageStat.Stat(laplacian_img)
            variance = stat.var[0] if len(stat.var) > 0 else 0

            # Normalize to 0-100 scale
            # Higher variance means sharper image
            sharpness_score = min(100, variance / 50 * 100)

            return sharpness_score
        except Exception as e:
            self.logger.warning(f"Error calculating sharpness: {str(e)}")
            return 50  # Default to middle value

    def _calculate_brightness(self, img):
        """Calculate image brightness"""
        try:
            # Convert to HSV for better brightness calculation
            hsv_img = img.convert('HSV')
            stat = ImageStat.Stat(hsv_img)

            # Get the average value (V in HSV)
            brightness = stat.mean[2]

            # Normalize to 0-100 scale
            # Penalize very dark or very bright images
            # Optimal brightness is around 128 (middle of 0-255)
            brightness_score = 100 - abs(brightness - 128) / 128 * 100

            return brightness_score
        except Exception as e:
            self.logger.warning(f"Error calculating brightness: {str(e)}")
            return 50  # Default to middle value

    def _calculate_contrast(self, gray_img):
        """Calculate image contrast"""
        try:
            # Get image statistics
            stat = ImageStat.Stat(gray_img)

            # Calculate standard deviation of pixel values
            std_dev = stat.stddev[0] if len(stat.stddev) > 0 else 0

            # Normalize to 0-100 scale
            # Higher standard deviation means more contrast
            contrast_score = min(100, std_dev / 60 * 100)

            return contrast_score
        except Exception as e:
            self.logger.warning(f"Error calculating contrast: {str(e)}")
            return 50  # Default to middle value

    def _calculate_color_diversity(self, img):
        """Calculate color diversity in the image"""
        try:
            # If image is not RGB, convert it
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Get image statistics for each channel
            r, g, b = img.split()
            r_std = ImageStat.Stat(r).stddev[0]
            g_std = ImageStat.Stat(g).stddev[0]
            b_std = ImageStat.Stat(b).stddev[0]

            # Calculate average standard deviation across channels
            avg_std = (r_std + g_std + b_std) / 3

            # Normalize to 0-100 scale
            color_score = min(100, avg_std / 50 * 100)

            return color_score
        except Exception as e:
            self.logger.warning(f"Error calculating color diversity: {str(e)}")
            return 50  # Default to middle value

    def filter_quality_images(self, image_paths, min_quality_score=60):
        """
        Filter images based on quality assessment

        Args:
            image_paths (list): List of image file paths
            min_quality_score (float): Minimum quality score (0-100) to accept

        Returns:
            list: Filtered list of image paths sorted by quality (highest first)
        """
        if not image_paths:
            return []

        # Assess quality of each image
        image_quality = []
        for path in image_paths:
            score, details = self.assess_image_quality(path)
            image_quality.append((path, score, details))

        # Filter images that meet minimum quality
        quality_images = [item for item in image_quality if item[1] >= min_quality_score]

        # If no images meet quality threshold, take the best ones we have (up to 3)
        if not quality_images and image_paths:
            self.logger.warning(f"No images meet quality threshold of {min_quality_score}. Using best available.")
            # Sort by quality score and take top 3
            image_quality.sort(key=lambda x: x[1], reverse=True)
            quality_images = image_quality[:min(3, len(image_quality))]

        # Sort by quality score (highest first)
        quality_images.sort(key=lambda x: x[1], reverse=True)

        # Log quality assessment results
        self.logger.info(f"Image quality assessment results:")
        for path, score, details in quality_images:
            self.logger.info(f"  - {os.path.basename(path)}: {score:.2f}/100")

        # Return just the paths, sorted by quality
        return [item[0] for item in quality_images]

    def select_featured_image(self, images):
        """Select the most suitable image as featured image"""
        if not images:
            return None

        # Filter for quality and get the best image
        quality_images = self.filter_quality_images(images)

        # Return the highest quality image, or the first one if filtering returned nothing
        return quality_images[0] if quality_images else images[0]

    def cleanup(self):
        """Clean up resources"""
        try:
            # Clean up temporary files
            for file in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    self.logger.error(f"Error deleting file {file_path}: {str(e)}")
        except Exception as e:
            self.logger.error(f"Error in cleanup: {str(e)}")

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()