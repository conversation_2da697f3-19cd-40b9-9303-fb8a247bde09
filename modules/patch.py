# -*- coding: utf-8 -*-
"""
Created on Sun May 23 14:44:43 2021

@author: Yicong
"""
#!/usr/bin/env python3
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import WebDriverException, SessionNotCreatedException
import sys
import os
import urllib.request
import re
import zipfile
import stat
import json
import shutil
import ssl
from sys import platform

def webdriver_executable():
    if platform == "linux" or platform == "linux2" or platform == "darwin":
        return 'chromedriver'
    return 'chromedriver.exe'

def download_lastest_chromedriver(current_chrome_version=""):
    def get_platform_filename():
        filename = ''

        if platform == "linux" or platform == "linux2":
            # linux
            filename += 'linux64'

        elif platform == "darwin":
            # OS X
            filename += 'mac-x64'
        elif platform == "win32":
            # Windows...
            filename += 'win32'

        return filename

    # Find the latest chromedriver, download, unzip, set permissions to executable.

    result = False
    try:
        # Create SSL context that doesn't verify certificates (for development)
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        url = 'https://googlechromelabs.github.io/chrome-for-testing/latest-versions-per-milestone-with-downloads.json'

        # Download latest chromedriver with SSL context
        request = urllib.request.Request(url)
        with urllib.request.urlopen(request, context=ssl_context) as stream:
            content = json.loads(stream.read().decode('utf-8'))

        # Parse the latest version.
        driver_url = None

        if current_chrome_version != "":
            # Extract major version number from Chrome version
            match = re.search(r'(\d+)', current_chrome_version)
            if match:
                major_version = match.group(1)
                if major_version in content["milestones"]:
                    downloads = content["milestones"][major_version]
                    for download in downloads["downloads"]["chromedriver"]:
                        if download["platform"] == get_platform_filename():
                            driver_url = download["url"]
                            break

        # If no specific version found or no version specified, get the latest stable
        if not driver_url:
            # Get the latest stable version
            latest_versions = sorted(content["milestones"].keys(), key=int, reverse=True)
            for version in latest_versions:
                downloads = content["milestones"][version]
                for download in downloads["downloads"]["chromedriver"]:
                    if download["platform"] == get_platform_filename():
                        driver_url = download["url"]
                        break
                if driver_url:
                    break

        if not driver_url:
            raise Exception("Could not find compatible ChromeDriver for platform")

        # Download the file.
        print('[INFO] downloading chromedriver ver: %s: %s'% (current_chrome_version, driver_url))
        file_name = driver_url.split("/")[-1]
        app_path = os.getcwd()
        chromedriver_path = os.path.normpath(os.path.join(app_path, 'modules', 'webdriver', webdriver_executable()))
        file_path = os.path.normpath(os.path.join(app_path, 'modules', 'webdriver', file_name))

        # Download with SSL context
        request = urllib.request.Request(driver_url)
        with urllib.request.urlopen(request, context=ssl_context) as response:
            with open(file_path, 'wb') as f:
                shutil.copyfileobj(response, f)

        # Unzip the file into folder

        webdriver_path = os.path.normpath(os.path.join(app_path, 'modules', 'webdriver'))
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            for member in zip_file.namelist():
                filename = os.path.basename(member)
                if not filename:
                    continue
                # Only extract the chromedriver executable
                if filename == webdriver_executable():
                    source = zip_file.open(member)
                    target = open(os.path.join(webdriver_path, filename), "wb")
                    with source, target:
                        shutil.copyfileobj(source, target)
                    break

        st = os.stat(chromedriver_path)
        os.chmod(chromedriver_path, st.st_mode | stat.S_IEXEC)
        print('[INFO] latest chromedriver downloaded')
        # Cleanup.
        os.remove(file_path)
        result = True
    except Exception as e:
        print(f"[ERROR] Failed to download ChromeDriver: {e}")
        print("[WARN] unable to download latest chromedriver. the system will use the local version instead.")

    return result