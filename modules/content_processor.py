import markdown2
import logging
import os
import re
from urllib.parse import urlparse
from config.config import REQUIRED_ELEMENTS, ADSENSE_SCRIPT
from modules.wordpress_integration import WordPressIntegration

class ContentProcessor:
    def __init__(self, wordpress_integration=None):
        self.setup_logging()
        self.adsense_script = ADSENSE_SCRIPT
        # Use the provided WordPress integration or create a new one
        self.wordpress = wordpress_integration or WordPressIntegration()
        self.logger.info("ContentProcessor initialized")

    def setup_logging(self):
        self.logger = logging.getLogger(__name__)

    def is_valid_url(self, url):
        """Check if the given string is a valid URL"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False

    def convert_markdown_to_html(self, markdown_content):
        """Convert markdown content to HTML"""
        try:
            # Convert markdown to HTML
            html_content = markdown2.markdown(
                markdown_content,
                extras=['tables', 'fenced-code-blocks', 'break-on-newline']
            )
            return html_content
        except Exception as e:
            self.logger.error(f"Error converting markdown to HTML: {str(e)}")
            raise

    def insert_images(self, html_content, image_paths):
        """Insert images into the HTML content with proper structure"""
        try:
            if not image_paths:
                self.logger.warning("No images provided for insertion")
                return html_content

            # Upload images to WordPress and get their URLs
            image_data = []
            for img_path in image_paths:
                try:
                    if not os.path.exists(img_path):
                        self.logger.error(f"Image file not found: {img_path}")
                        continue

                    media_data = self.wordpress.upload_media(img_path)
                    if media_data:
                        image_data.append(media_data)
                        self.logger.info(f"Successfully uploaded and added image: {img_path}")
                except Exception as e:
                    self.logger.error(f"Error uploading image {img_path}: {str(e)}")
                    continue

            if not image_data:
                self.logger.warning("No images were successfully uploaded")
                return html_content

            # Split content into paragraphs
            paragraphs = html_content.split('</p>')
            new_content = []
            image_index = 0

            # Calculate image distribution
            total_paragraphs = len(paragraphs)
            total_images = len(image_data)

            # Ensure we don't place images at the very beginning or end
            usable_paragraph_count = max(1, total_paragraphs - 2)  # Reserve first and last paragraph

            # Calculate image placement intervals - IMPROVED DISTRIBUTION
            if total_images > 0 and usable_paragraph_count > 0:
                # Distribute images evenly throughout the entire article
                # Skip the first paragraph (intro) and the last paragraph (conclusion)
                start_pos = 1  # Skip intro paragraph
                end_pos = total_paragraphs - 1  # Skip conclusion paragraph

                if total_images == 1:
                    # If only one image, place it in the middle
                    image_positions = [start_pos + (end_pos - start_pos) // 2]
                else:
                    # Calculate positions to distribute evenly
                    step = (end_pos - start_pos) / (total_images + 1)
                    image_positions = [int(start_pos + step * (i + 1)) for i in range(total_images)]

                # Ensure all positions are valid
                image_positions = [pos for pos in image_positions if start_pos <= pos < end_pos]

                self.logger.info(f"Distributing {total_images} images across {total_paragraphs} paragraphs at positions: {image_positions}")
            else:
                image_positions = []

            # Process each paragraph
            for i, paragraph in enumerate(paragraphs):
                new_content.append(paragraph)

                # Add AdSense after the first paragraph (intro)
                if i == 0:
                    new_content.append(self.adsense_script)

                # Add image at calculated positions
                if i in image_positions and image_index < len(image_data):
                    img_data = image_data[image_index]
                    new_content.append(f"""
                    <div class="blog-image-container" style="margin: 20px 0; text-align: center;">
                        <img src="{img_data['url']}"
                             alt="Blog image"
                             class="blog-image"
                             style="max-width: 100%; height: auto; border-radius: 8px;"
                             loading="lazy">
                    </div>
                    """)
                    image_index += 1

                    # Add AdSense after every image
                    new_content.append(self.adsense_script)

                if i < len(paragraphs) - 1:
                    new_content.append('</p>')

            # If we still have unused images, distribute them evenly in the second half of the article
            # This ensures we don't cluster all remaining images at the end
            if image_index < len(image_data) and total_paragraphs > 3:
                remaining_images = len(image_data) - image_index

                # Focus on the second half of the article for remaining images
                second_half_start = total_paragraphs // 2
                second_half_end = total_paragraphs - 1  # Skip the last paragraph

                # Calculate positions in the second half
                if remaining_images == 1:
                    # If only one remaining image, place it in the middle of the second half
                    additional_positions = [second_half_start + (second_half_end - second_half_start) // 2]
                else:
                    # Distribute remaining images evenly in the second half
                    step = (second_half_end - second_half_start) / (remaining_images + 1)
                    additional_positions = [int(second_half_start + step * (i + 1)) for i in range(remaining_images)]

                # Filter out positions that already have images
                additional_positions = [pos for pos in additional_positions if pos not in image_positions and pos < second_half_end]

                self.logger.info(f"Adding {remaining_images} remaining images at additional positions: {additional_positions}")

                # Insert remaining images at these positions
                for pos in additional_positions:
                    if image_index >= len(image_data):
                        break

                    img_data = image_data[image_index]
                    img_html = f"""
                    <div class="blog-image-container" style="margin: 20px 0; text-align: center;">
                        <img src="{img_data['url']}"
                             alt="Blog image"
                             class="blog-image"
                             style="max-width: 100%; height: auto; border-radius: 8px;"
                             loading="lazy">
                    </div>
                    """

                    # Calculate insertion point in new_content
                    # Each paragraph has at least 2 entries in new_content (paragraph content + </p>)
                    # Some paragraphs might have more if they contain images or AdSense
                    insertion_index = 0
                    paragraph_count = 0

                    for idx, item in enumerate(new_content):
                        if '</p>' in item:
                            paragraph_count += 1
                            if paragraph_count == pos:
                                insertion_index = idx + 1
                                break

                    if insertion_index > 0:
                        # Insert image and AdSense
                        new_content.insert(insertion_index, img_html)
                        new_content.insert(insertion_index + 1, self.adsense_script)
                        image_index += 1

            html_content = ''.join(new_content)
            self.logger.info(f"Successfully inserted {len(image_data)} images into content")
            return html_content
        except Exception as e:
            self.logger.error(f"Error inserting images: {str(e)}")
            return html_content

    def add_required_elements(self, html_content, required_elements):
        """Add required elements to the HTML content"""
        try:
            # Check for required elements
            for element in required_elements:
                # Normalize element name (lowercase, handle variations)
                normalized_element = self._normalize_element_name(element)

                if normalized_element in REQUIRED_ELEMENTS:
                    if REQUIRED_ELEMENTS[normalized_element] not in html_content:
                        self.logger.info(f"Adding {element} ({normalized_element}) to content")
                        html_content += self._create_element(normalized_element, element)
                else:
                    # Handle custom elements that don't match predefined ones
                    self.logger.info(f"Creating custom element for: {element}")
                    html_content += self._create_custom_element(element)

            return html_content
        except Exception as e:
            self.logger.error(f"Error adding required elements: {str(e)}")
            raise

    def _normalize_element_name(self, element):
        """Normalize element name to match predefined elements"""
        element_lower = element.lower().strip()

        # Handle common variations
        if 'table' in element_lower:
            if 'comparison' in element_lower or 'price' in element_lower:
                if 'price' in element_lower:
                    return 'price_comparison'
                else:
                    return 'comparison_table'
            else:
                return 'table'
        elif element_lower in ['list', 'bullet points', 'bullet_points']:
            return 'list'
        elif element_lower in ['quote', 'quotes', 'blockquote']:
            return 'quote'
        elif 'slider' in element_lower:
            return 'image_slider'
        elif 'code' in element_lower:
            return 'code_block'

        return element_lower

    def _create_element(self, normalized_element, original_element):
        """Create HTML element based on normalized type"""
        if normalized_element == 'table':
            return self._create_sample_table()
        elif normalized_element in ['list', 'bullet_points']:
            return self._create_bullet_points()
        elif normalized_element == 'quote':
            return self._create_quote()
        elif normalized_element == 'comparison_table':
            return self._create_comparison_table(original_element)
        elif normalized_element == 'price_comparison':
            return self._create_price_comparison_table(original_element)
        elif normalized_element == 'image_slider':
            return self._create_image_slider()
        elif normalized_element == 'code_block':
            return self._create_code_block()
        else:
            return self._create_custom_element(original_element)

    def _create_custom_element(self, element):
        """Create a custom element based on the element description"""
        return f"""
        <div class="custom-element" style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h4>Custom Element: {element}</h4>
            <p><em>This section would contain: {element}</em></p>
            <p>Please customize this content based on your specific requirements for "{element}".</p>
        </div>
        """

    def _create_sample_table(self):
        """Create a sample table"""
        return """
        <table>
            <thead>
                <tr>
                    <th>Header 1</th>
                    <th>Header 2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Data 1</td>
                    <td>Data 2</td>
                </tr>
            </tbody>
        </table>
        """

    def _create_bullet_points(self):
        """Create bullet points"""
        return """
        <ul>
            <li>Point 1</li>
            <li>Point 2</li>
            <li>Point 3</li>
        </ul>
        """

    def _create_image_slider(self):
        """Create image slider HTML"""
        return """
        <div class="image-slider">
            <div class="slider-container">
                <!-- Images will be added dynamically -->
            </div>
        </div>
        """

    def _create_code_block(self):
        """Create code block"""
        return """
        <pre><code>
        // Sample code block
        function example() {
            console.log("Hello, World!");
        }
        </code></pre>
        """

    def _create_quote(self):
        """Create a quote/blockquote element"""
        return """
        <blockquote style="margin: 20px 0; padding: 15px 20px; border-left: 4px solid #007cba; background-color: #f9f9f9; font-style: italic;">
            <p>"This is an important quote or insight related to the topic. It highlights key information that readers should pay attention to."</p>
            <footer style="margin-top: 10px; font-size: 0.9em; color: #666;">
                — <cite>Industry Expert</cite>
            </footer>
        </blockquote>
        """

    def _create_comparison_table(self, element_description):
        """Create a comparison table"""
        return f"""
        <div class="comparison-table-container" style="margin: 20px 0; overflow-x: auto;">
            <h4>{element_description}</h4>
            <table class="comparison-table" style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Feature</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Option A</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Option B</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Option C</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Price</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">$X,XXX</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">$X,XXX</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">$X,XXX</td>
                    </tr>
                    <tr style="background-color: #f9f9f9;">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Range</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">XXX km</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">XXX km</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">XXX km</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Features</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Feature list</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Feature list</td>
                        <td style="border: 1px solid #ddd; padding: 12px;">Feature list</td>
                    </tr>
                </tbody>
            </table>
        </div>
        """

    def _create_price_comparison_table(self, element_description):
        """Create a price comparison table specifically for vehicles like Ola Ather"""
        return f"""
        <div class="price-comparison-container" style="margin: 20px 0; overflow-x: auto;">
            <h4>{element_description}</h4>
            <table class="price-comparison" style="width: 100%; border-collapse: collapse; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #007cba; color: white;">
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Model</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Price (₹)</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Range (km)</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Top Speed</th>
                        <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Charging Time</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Ola S1 Pro</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">₹1,29,999</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">181 km</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">116 km/h</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">6.5 hours</td>
                    </tr>
                    <tr style="background-color: #f9f9f9;">
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Ather 450X</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">₹1,38,748</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">146 km</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">90 km/h</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">5.4 hours</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">TVS iQube</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">₹1,15,000</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">100 km</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">78 km/h</td>
                        <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">4.5 hours</td>
                    </tr>
                </tbody>
            </table>
            <p style="font-size: 0.9em; color: #666; margin-top: 10px;">
                <em>*Prices are approximate and may vary based on location and current offers.</em>
            </p>
        </div>
        """

    def insert_adsense(self, html_content):
        """Insert AdSense script into the HTML content"""
        try:
            # Insert AdSense after the first paragraph
            paragraphs = html_content.split('</p>')
            if len(paragraphs) > 1:
                paragraphs[1] = paragraphs[1] + self.adsense_script
                html_content = '</p>'.join(paragraphs)
            else:
                html_content += self.adsense_script

            self.logger.info("Successfully inserted AdSense into content")
            return html_content
        except Exception as e:
            self.logger.error(f"Error inserting AdSense: {str(e)}")
            return html_content

    def process_outbound_links(self, html_content, outbound_links=None):
        """Process and add outbound links to the HTML content

        Args:
            html_content (str): The HTML content to process
            outbound_links (str or list): Comma-separated string or list of URLs to add as outbound links

        Returns:
            str: HTML content with outbound links added
        """
        try:
            if not outbound_links:
                self.logger.info("No outbound links provided")
                return html_content

            # Process links into a list if provided as string
            link_list = []
            if isinstance(outbound_links, str):
                # Log the raw input for debugging
                self.logger.info(f"Processing outbound links string: {outbound_links}")
                # Split by comma and clean
                link_list = [link.strip() for link in outbound_links.split(',') if link.strip()]
            elif isinstance(outbound_links, list):
                # Log the raw input for debugging
                self.logger.info(f"Processing outbound links list: {outbound_links}")
                # For each item in the list, check if it contains commas
                for item in outbound_links:
                    if isinstance(item, str):
                        if ',' in item:
                            # If the item contains commas, split it and add each part
                            for sublink in item.split(','):
                                if sublink.strip():
                                    link_list.append(sublink.strip())
                        else:
                            # Otherwise add the item as is
                            if item.strip():
                                link_list.append(item.strip())

            # Log the processed links
            self.logger.info(f"Processed link list: {link_list}")

            if not link_list:
                return html_content

            # Validate URLs
            valid_links = []
            for link in link_list:
                if self.is_valid_url(link):
                    valid_links.append(link)
                else:
                    self.logger.warning(f"Invalid URL skipped: {link}")

            if not valid_links:
                self.logger.warning("No valid outbound links found")
                return html_content

            # Create outbound links section with better styling
            outbound_section = """
            <div class="outbound-links-section" style="margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #4285f4;">
                <h3 style="margin-top: 0; color: #333;">Related Resources</h3>
                <ul class="outbound-links" style="list-style-type: none; padding-left: 0;">
            """

            for link in valid_links:
                try:
                    # Extract domain for display
                    domain = urlparse(link).netloc
                    # Make the domain more readable by removing www. if present
                    display_text = domain.replace('www.', '')

                    # Try to extract a more meaningful name from the path
                    path = urlparse(link).path
                    if path and path != '/':
                        # Get the last part of the path and replace hyphens with spaces
                        path_parts = [p for p in path.split('/') if p]
                        if path_parts:
                            last_part = path_parts[-1].replace('-', ' ').replace('_', ' ')
                            # Capitalize words for better readability
                            display_text = ' '.join(word.capitalize() for word in last_part.split())

                    outbound_section += f"""
                        <li style="margin-bottom: 10px;">
                            <a href="{link}" target="_blank" rel="noopener noreferrer"
                               style="color: #1a73e8; text-decoration: none; display: inline-block; padding: 5px 10px; border: 1px solid #dadce0; border-radius: 4px;">
                                <span style="font-weight: bold;">{display_text}</span>
                                <small style="display: block; color: #5f6368; font-size: 0.8em;">{domain}</small>
                            </a>
                        </li>
                    """
                except Exception as e:
                    # If there's an error processing this link, use a simpler format
                    self.logger.warning(f"Error formatting link {link}: {str(e)}")
                    outbound_section += f"""
                        <li style="margin-bottom: 10px;">
                            <a href="{link}" target="_blank" rel="noopener noreferrer"
                               style="color: #1a73e8; text-decoration: none;">
                                {link}
                            </a>
                        </li>
                    """

            outbound_section += """
                </ul>
            </div>
            """

            # Add the outbound links section before the last paragraph
            paragraphs = html_content.split('</p>')
            if len(paragraphs) > 1:
                # Insert before the last paragraph
                paragraphs[-2] = paragraphs[-2] + outbound_section
                html_content = '</p>'.join(paragraphs)
            else:
                # If there's only one paragraph, add at the end
                html_content += outbound_section

            self.logger.info(f"Successfully added {len(valid_links)} outbound links to content")
            return html_content

        except Exception as e:
            self.logger.error(f"Error processing outbound links: {str(e)}")
            return html_content