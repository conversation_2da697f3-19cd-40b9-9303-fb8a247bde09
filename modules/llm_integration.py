import requests
import logging
from config.config import OLLAMA_URL, MODEL_NAME, MAX_TOKENS, LONG_ARTICLE_THRESHOLD

class LLMIntegration:
    def __init__(self):
        self.setup_logging()
        self.base_url = OLLAMA_URL
        self.model_name = MODEL_NAME

    def setup_logging(self):
        self.logger = logging.getLogger(__name__)

    def clean_text(self, text):
        """Clean and format text input"""
        if not text:
            return ""
        # Remove extra quotes and whitespace
        text = text.strip().strip('"\'')
        return text

    def generate_raw_content(self, prompt, max_tokens=None, timeout=60):
        """Generate raw content using the LLM with a custom prompt"""
        try:
            # Use provided max_tokens or default from config
            if max_tokens is None:
                max_tokens = MAX_TOKENS

            # Make request to Ollama
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "max_tokens": max_tokens
                    }
                },
                timeout=timeout
            )
            response.raise_for_status()

            # Get the generated content
            content = response.json().get('response', '')
            if not content:
                raise ValueError("Empty response from LLM")

            return content
        except Exception as e:
            self.logger.error(f"Error generating raw content: {str(e)}")
            raise

    def generate_content(self, title, topic, keywords, context, word_count=1000, must_have_elements=None):
        """Generate blog content using Gemma 3"""
        try:
            # Clean and format inputs
            title = self.clean_text(title)
            topic = self.clean_text(topic)
            keywords = self.clean_text(keywords)
            context = self.clean_text(context)

            # Convert word_count to integer
            word_count = int(word_count)  # Ensure word_count is an integer

            # For longer articles, use the manager agent
            if word_count >= LONG_ARTICLE_THRESHOLD:
                self.logger.info(f"Article length ({word_count} words) exceeds threshold ({LONG_ARTICLE_THRESHOLD}). Using manager agent.")
                # Import here to avoid circular imports
                from modules.manager_agent import ManagerAgent
                manager = ManagerAgent(self)
                return manager.generate_long_article(title, topic, keywords, context, word_count, must_have_elements)

            # For shorter articles, use the standard approach
            # Calculate word count range
            min_words = max(500, int(word_count * 0.8))
            max_words = int(word_count * 1.2)

            # Prepare must-have elements section
            elements_instruction = ""
            if must_have_elements:
                elements_list = [elem.strip() for elem in must_have_elements if elem.strip()]
                if elements_list:
                    elements_instruction = f"""
9. MUST INCLUDE these specific elements in your content:
   {', '.join(elements_list)}
   - For tables: Create detailed comparison tables with relevant data
   - For lists: Use bullet points or numbered lists to organize information
   - For quotes: Include relevant quotes from industry experts or important statements
   - For price comparisons: Create detailed pricing tables with specifications
   - Integrate these elements naturally within the content flow, not just at the end"""

            # Construct the prompt
            prompt = f"""You are a professional content writer specializing in electric vehicles. Write a detailed, informative blog post with the following specifications:

Title: {title}
Main Topic: {topic}
Keywords to include: {keywords}
Context: {context}

Requirements:
1. Write in a professional, engaging tone suitable for an electric vehicle industry website
2. Include an attention-grabbing introduction
3. Provide detailed analysis and insights about {topic}
4. Incorporate the specified keywords naturally throughout the content
5. Include relevant statistics or data points where applicable
6. End with a strong conclusion that summarizes key points
7. Ensure the content is well-structured with proper headings and paragraphs
8. Maintain a word count between {min_words}-{max_words} words{elements_instruction}

Format the response in markdown with appropriate headings, bullet points, and paragraphs. Make sure to integrate any required elements naturally within the content structure."""

            # Generate content using the raw content method
            content = self.generate_raw_content(prompt, timeout=60)
            self.logger.info("Successfully generated content using Gemma")
            return content
        except requests.exceptions.ConnectionError:
            error_msg = "Could not connect to Ollama. Please make sure Ollama is running and Gemma model is installed."
            self.logger.error(error_msg)
            # Return a fallback message instead of raising an exception
            return f"""*Note: This content was generated as a fallback because Ollama could not be reached. Please ensure Ollama is running with the Gemma model installed.*

## About {topic}

This is a placeholder article about {topic}. The actual article would include information about:

- {keywords}
- {context}

Please start Ollama and try again to generate a complete article."""
        except requests.exceptions.Timeout:
            error_msg = "Request to Ollama timed out. Please try again."
            self.logger.error(error_msg)
            # Return a fallback message instead of raising an exception
            return f"""*Note: This content was generated as a fallback because the request to Ollama timed out.*

## About {topic}

This is a placeholder article about {topic}. The actual article would include information about:

- {keywords}
- {context}

Please try again to generate a complete article."""
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error generating content with Gemma: {error_msg}")

            # Provide more specific error message for common issues
            if "'target_words'" in error_msg:
                self.logger.error("The error is related to the 'target_words' field in the article structure")
                error_msg = "Issue with article structure. The system will use a more robust approach next time."

            # Return a fallback message instead of raising an exception
            return f"""*Note: This content was generated as a fallback due to an error: {error_msg}*

## About {topic}

This is a placeholder article about {topic}. The actual article would include information about:

- {keywords}
- {context}

Please try again with a different word count or topic structure. The system has been updated to handle this case better in the future."""