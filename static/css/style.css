/* Premium UI Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-100);
    background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem;
}

/* Header styles */
header {
    text-align: center;
    margin-bottom: 2.5rem;
    padding: 2rem;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

header h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 2.5rem;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
    letter-spacing: -0.5px;
}

header p {
    color: var(--gray-600);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Main content layout */
main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Form styles */
.form-container {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    border: 1px solid var(--gray-200);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.form-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
}

.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.form-section h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
}

.form-section h3 i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-size: 1rem;
}

.form-row {
    display: flex;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group.half {
    flex: 1;
    min-width: 0;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
}

.toggle-password:hover {
    color: var(--gray-700);
    background: none;
    transform: translateY(-50%);
    box-shadow: none;
}

.toggle-password::after {
    display: none;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.95rem;
}

input[type="text"],
input[type="url"],
input[type="password"],
input[type="number"] {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    font-size: 1rem;
    transition: all var(--transition-fast);
    background-color: var(--light-color);
    color: var(--gray-800);
}

input[type="text"]:hover,
input[type="url"]:hover,
input[type="password"]:hover,
input[type="number"]:hover {
    border-color: var(--gray-400);
}

input[type="text"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

input::placeholder {
    color: var(--gray-400);
}

small {
    display: block;
    margin-top: 0.5rem;
    color: var(--gray-500);
    font-size: 0.85rem;
}

.tag-counter {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--gray-500);
}

.tag-counter.warning {
    color: var(--warning-color);
}

.tag-counter.error {
    color: var(--error-color);
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(67, 97, 238, 0.2), 0 2px 4px -1px rgba(67, 97, 238, 0.1);
}

button:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(67, 97, 238, 0.25), 0 4px 6px -1px rgba(67, 97, 238, 0.15);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px -1px rgba(67, 97, 238, 0.2);
}

button:disabled {
    background-color: var(--gray-400);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

button::after {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 0.5rem;
    vertical-align: middle;
    border-top: 0.3em solid transparent;
    border-right: 0;
    border-bottom: 0.3em solid transparent;
    border-left: 0.3em solid;
    transition: transform var(--transition-fast);
}

button:hover::after {
    transform: translateX(3px);
}

button.loading {
    position: relative;
    color: transparent;
}

button.loading::before {
    content: '';
    position: absolute;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Terminal output styles */
.output-container {
    background-color: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    border: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
}

.output-container h2 {
    margin-bottom: 0.75rem;
    color: var(--gray-800);
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.output-container h2::before {
    content: '>';
    display: inline-block;
    margin-right: 0.5rem;
    color: var(--primary-color);
    font-weight: 700;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
}

.status-indicator {
    display: flex;
    align-items: center;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--success-color);
    margin-right: 0.5rem;
    position: relative;
}

.status-dot::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    border: 2px solid var(--success-color);
    opacity: 0.5;
    animation: pulse 1.5s infinite;
}

.status-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--gray-700);
}

.status-indicator.processing .status-dot {
    background-color: var(--warning-color);
}

.status-indicator.processing .status-dot::after {
    border-color: var(--warning-color);
}

.status-indicator.error .status-dot {
    background-color: var(--error-color);
}

.status-indicator.error .status-dot::after {
    border-color: var(--error-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.icon-button {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    box-shadow: none;
}

.icon-button:hover {
    color: var(--gray-800);
    background-color: var(--gray-200);
    transform: none;
    box-shadow: none;
}

.icon-button::after {
    display: none;
}

.terminal {
    background-color: var(--gray-900);
    color: #e2e8f0;
    padding: 1.25rem;
    border-radius: var(--border-radius-md);
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    height: 450px;
    overflow-y: auto;
    flex-grow: 1;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    border: 1px solid var(--gray-800);
}

.terminal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 28px;
    background-color: var(--gray-800);
    border-top-left-radius: var(--border-radius-md);
    border-top-right-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    padding: 0 12px;
    z-index: 1;
}

.terminal::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 12px;
    width: 12px;
    height: 12px;
    background-color: #ff5f56;
    border-radius: 50%;
    box-shadow: 20px 0 0 #ffbd2e, 40px 0 0 #27c93f;
    z-index: 2;
}

#logs {
    white-space: pre-wrap;
    word-break: break-word;
    padding-top: 28px;
    font-size: 0.9rem;
    line-height: 1.5;
}

#logs .info {
    color: #4cc9f0;
}

#logs .warning {
    color: #f59e0b;
}

#logs .error {
    color: #ef4444;
}

#logs .success {
    color: #10b981;
}

/* Footer styles */
footer {
    text-align: center;
    margin-top: 2.5rem;
    padding: 1.5rem;
    color: var(--gray-600);
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

footer a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

footer a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .container {
        padding: 1.5rem;
    }

    header {
        padding: 1.5rem;
    }

    header h1 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }

    .form-container, .output-container {
        margin-bottom: 1.5rem;
    }

    .form-container:hover {
        transform: none;
    }

    header h1 {
        font-size: 1.75rem;
    }

    .terminal {
        height: 350px;
    }
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: transform var(--transition-normal);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    color: var(--gray-500);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close-modal:hover {
    color: var(--gray-900);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--gray-800);
    margin: 1.5rem 0 0.75rem;
}

.modal-body h4:first-child {
    margin-top: 0;
}

.modal-body p {
    margin-bottom: 1rem;
    color: var(--gray-700);
}

.modal-body ul {
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.modal-body strong {
    color: var(--gray-900);
    font-weight: 600;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.2); opacity: 0.2; }
    100% { transform: scale(1); opacity: 0.5; }
}

.container {
    animation: fadeIn 0.5s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-800);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-600);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}
