# EV Blog Automation Suite

An automated system for generating and publishing blog posts about electric vehicles to WordPress using AI, Google Sheets integration, and image processing.

![EV Blog Automation Suite](https://img.shields.io/badge/EV%20Blog-Automation%20Suite-blue)
![Python 3.6+](https://img.shields.io/badge/Python-3.6%2B-brightgreen)
![License](https://img.shields.io/badge/License-MIT-yellow)

## Features

- **Web Interface**: User-friendly interface for configuring and running the blog automation process
- **Google Sheets Integration**: Pull blog post data from a Google Sheet
- **AI-Powered Content**: Generate high-quality blog content using Ollama and Gemma
- **Image Search**: Automatically find and download relevant images
- **WordPress Publishing**: Publish posts directly to your WordPress site
- **Customizable**: Configure the number of images, article length, tags, and categories
- **Long Article Generation**: Special handling for articles over 2000 words with structured sections
- **Tags & Categories**: Add up to 9 tags and 3 categories to your posts

## Prerequisites

- Python 3.6 or higher
- Ollama with Gemma model installed (for AI content generation)
- Google Sheet with blog post data
- WordPress site with admin credentials

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/ev-blog-automation.git
   cd ev-blog-automation
   ```

2. **Run the setup script**:
   ```bash
   python3 setup.py
   ```
   This will:
   - Check your Python version
   - Install required dependencies
   - Create necessary directories
   - Check for Ollama and Gemma model (optional)

3. **Install Ollama** (if not already installed):
   - Download from [ollama.ai/download](https://ollama.ai/download)
   - Install the Gemma model:
     ```bash
     ollama pull gemma3:latest
     ```

4. **Configure environment variables**:
   - Copy the example environment file:
     ```bash
     cp .env.example .env
     ```
   - Edit the `.env` file with your credentials and API keys

5. **Verify WordPress credentials** (optional but recommended):
   - Run the WordPress credentials checker:
     ```bash
     python3 check_wordpress.py
     ```
   - This will verify that your WordPress credentials work and have the necessary permissions

## Usage

1. **Start the web interface**:
   ```bash
   python3 run_web_interface.py
   ```
   This will open your browser to the web interface.

2. **Enter your configuration**:
   - **Google Sheet ID**: The ID from your Google Sheets URL
   - **WordPress URL**: Your WordPress site URL
   - **WordPress Username**: Your WordPress admin username
   - **WordPress Password**: Your WordPress admin password
   - **Number of Images**: Maximum number of images per post (default: 3)
   - **Article Length**: Target word count for generated articles (default: 1000)
   - **Tags**: Up to 9 comma-separated tags for your posts
   - **Categories**: Up to 3 comma-separated categories for your posts

3. **Click "Generate Articles"** to start the process

4. **Monitor the process** in the terminal output display

## Google Sheet Format

Your Google Sheet should have the following columns:
- **topic name**: The main topic of the blog post
- **title**: The title of the blog post
- **keywords**: Keywords to include in the content
- **context**: Additional context for the AI
- **must have elements**: Required elements (e.g., table, bullet points)
- **status**: Current status of the post

Example row:
| topic name | title | keywords | context | must have elements | status |
|------------|-------|----------|---------|-------------------|--------|
| tata nexon ev battery warranty | Tata Nexon EV Battery Warranty Explained | Nexon EV battery warranty | EV service policy | Tata EV terms | bullet points |

The Google Sheet must be publicly accessible for reading.

## Long Article Generation

For articles with a target word count of 2000 or more words, the system uses a special multi-section generation approach:

1. A manager agent creates a structured outline for the article
2. The article is split into logical sections
3. Each section is generated independently
4. All sections are combined into a cohesive article

This approach produces better structured, more coherent long-form content.

## Tags and Categories

The system supports adding tags and categories to your blog posts:

- **Tags**: Keywords that help categorize your posts (up to 9 tags)
- **Categories**: Broader groupings for your posts (up to 3 categories)

These taxonomies help organize your WordPress content and improve SEO.

## Project Structure

- `run_web_interface.py`: Web interface entry point
- `web_interface.py`: Flask web application
- `main.py`: Command-line entry point
- `config/`: Configuration files
- `modules/`: Core functionality modules
  - `llm_integration.py`: AI content generation
  - `manager_agent.py`: Long article generation
  - `wordpress_integration.py`: WordPress publishing
  - `image_handler.py`: Image search and processing
  - `google_sheets.py`: Google Sheets integration
  - `content_processor.py`: Content formatting and processing
- `static/`: Web interface assets (CSS, JavaScript)
- `templates/`: HTML templates for web interface
- `logs/`: Log files
- `temp/`: Temporary files (downloaded images, etc.)

## Troubleshooting

- **Import Errors**: Run `python3 setup.py` to install missing dependencies
- **Ollama Connection Issues**: Make sure Ollama is running with `ollama serve`
- **Google Sheet Access**: Ensure your Google Sheet is publicly accessible
- **WordPress Connection**: Verify your WordPress credentials and URL with `python3 check_wordpress.py`
- **401 Unauthorized Errors**: Check your WordPress credentials or try using an Application Password
  - In WordPress admin, go to Users → Your Profile → Application Passwords
  - Create a new application password specifically for this application
- **Image Upload Issues**: Ensure your WordPress user has permission to upload media
- **Long Article Generation**: If you encounter issues with long articles, try reducing the word count
- **Tags and Categories**: If tags or categories aren't being applied, check your WordPress user permissions

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Ollama for providing the AI model infrastructure
- Google Sheets API for data management
- WordPress API for publishing integration