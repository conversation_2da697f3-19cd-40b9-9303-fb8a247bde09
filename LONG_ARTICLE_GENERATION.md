# Long Article Generation Feature

This document explains the new long article generation feature that has been added to the EV Blog Automation Suite.

## Overview

The system now supports generating longer blog posts (2000+ words) by using a manager agent that:

1. Creates a structured outline for the article
2. Splits the article into logical sections
3. Generates content for each section independently
4. Combines all sections into a cohesive article

This approach overcomes the limitations of LLMs when generating very long content and produces better structured, more coherent articles.

## How It Works

### Threshold-Based Approach

- For articles under 2000 words: The system uses the standard content generation approach
- For articles 2000+ words: The system uses the manager agent to handle the content generation

### Manager Agent Process

1. **Structure Generation**:
   - The manager agent first creates a detailed outline for the article
   - This includes a title, introduction, multiple body sections, and a conclusion
   - Each section has a heading, description, and target word count

2. **Section Generation**:
   - For each section in the structure, the manager agent generates content independently
   - Each section is given appropriate context from the overall article
   - The content is tailored to match the section's heading and description

3. **Content Assembly**:
   - All sections are combined into a single cohesive article
   - The system ensures proper formatting and transitions between sections

## Configuration

The long article generation feature can be configured in `config/config.py`:

- `LONG_ARTICLE_THRESHOLD`: Word count threshold for using the manager agent (default: 2000)
- `MAX_TOKENS`: Maximum tokens for LLM requests (default: 2000)

## Using the Feature

To use the long article generation feature:

1. In the web interface, set the "Article Length" to 2000 or higher
2. The system will automatically use the manager agent for content generation
3. The process will take longer than standard generation but will produce better structured content

## Benefits

- **Better Structure**: Articles have a more logical flow and organization
- **More Comprehensive Content**: Each section can focus on a specific aspect of the topic
- **Improved Coherence**: By planning the structure first, the content is more cohesive
- **Overcomes Token Limitations**: Bypasses the token limit constraints of LLMs

## Technical Implementation

The feature is implemented through:

- `modules/manager_agent.py`: Contains the ManagerAgent class that handles the orchestration
- Updates to `modules/llm_integration.py`: Modified to use the manager agent for longer articles
- Configuration in `config/config.py`: Settings for thresholds and token limits

## Example Structure

A typical article structure generated by the manager agent looks like:

```json
{
  "title": "The Future of Electric Vehicles: Trends and Innovations",
  "introduction": {
    "heading": "Introduction",
    "description": "Overview of the current EV market and the importance of innovation",
    "target_words": 300
  },
  "sections": [
    {
      "heading": "Battery Technology Advancements",
      "description": "Recent breakthroughs in battery capacity, charging speed, and lifespan",
      "target_words": 500
    },
    {
      "heading": "Autonomous Driving Features",
      "description": "Integration of self-driving capabilities in modern EVs",
      "target_words": 500
    },
    {
      "heading": "Sustainable Manufacturing",
      "description": "Eco-friendly production methods and materials for EVs",
      "target_words": 400
    }
  ],
  "conclusion": {
    "heading": "Conclusion",
    "description": "Summary of key trends and outlook for the future",
    "target_words": 300
  }
}
```
