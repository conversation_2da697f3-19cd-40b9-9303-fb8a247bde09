# Tags and Categories Feature

This document explains the new tags and categories feature that has been added to the EV Blog Automation Suite.

## Overview

The system now supports adding tags and categories to blog posts:

1. **Tags**: Keywords that help categorize your posts (up to 9 tags)
2. **Categories**: Broader groupings for your posts (up to 3 categories)

These taxonomies help organize your WordPress content and improve SEO by providing better content structure.

## How It Works

### User Interface

- The web interface now includes fields for tags and categories in the Content Settings section
- Tags are limited to a maximum of 9 per post
- Categories are limited to a maximum of 3 per post
- Both fields use comma-separated values (e.g., "electric cars, battery technology, charging")
- Real-time counters show how many tags/categories have been added

### Backend Processing

1. **Input Validation**:
   - Tags and categories are validated and limited to their maximum counts
   - Empty values are filtered out
   - Whitespace is trimmed from each value

2. **WordPress Integration**:
   - The system checks if each tag/category already exists in WordPress
   - If a tag/category doesn't exist, it's created automatically
   - Tags and categories are then associated with the published post

## Using the Feature

To use the tags and categories feature:

1. Enter comma-separated tags in the "Tags" field (up to 9)
2. Enter comma-separated categories in the "Categories" field (up to 3)
3. The counters will update in real-time to show how many you've added
4. If you exceed the limits, the counters will turn yellow (warning) or red (error)
5. When you click "Generate Articles", the tags and categories will be applied to all posts

## Benefits

- **Better Organization**: Makes it easier to navigate and find related content
- **Improved SEO**: Helps search engines understand your content structure
- **Enhanced User Experience**: Allows visitors to browse related content
- **Content Discovery**: Increases the chances of readers finding more of your content

## Technical Implementation

The feature is implemented through:

- Updates to `wordpress_integration.py`: Added methods to handle tags and categories
- Updates to `web_interface.py`: Modified to pass tags and categories to WordPress
- New UI elements in `index.html`: Added fields for tags and categories
- JavaScript in `script.js`: Added real-time validation and counting
- CSS styling: Added styles for tag counters

## Example Usage

**Tags Example**: 
```
electric vehicles, battery technology, charging infrastructure, range anxiety, EV adoption
```

**Categories Example**:
```
Technology, Sustainability, Transportation
```

When these are submitted, the system will:
1. Create any tags or categories that don't already exist in WordPress
2. Associate them with the published post
3. Log the process in the terminal output

## Notes

- Tags and categories are applied to all posts generated in a single run
- If you need different tags/categories for different posts, run the tool separately for each set
- The system handles duplicate tags and categories automatically (they won't be created twice)
