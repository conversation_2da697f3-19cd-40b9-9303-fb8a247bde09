# Contributing to EV Blog Automation Suite

Thank you for considering contributing to the EV Blog Automation Suite! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and considerate of others.

## How Can I Contribute?

### Reporting Bugs

If you find a bug, please create an issue with the following information:

- A clear, descriptive title
- Steps to reproduce the issue
- Expected behavior
- Actual behavior
- Screenshots (if applicable)
- Environment details (OS, Python version, etc.)

### Suggesting Enhancements

If you have an idea for an enhancement, please create an issue with:

- A clear, descriptive title
- A detailed description of the proposed enhancement
- Any relevant examples or mockups
- Why this enhancement would be useful to most users

### Pull Requests

1. Fork the repository
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests to ensure your changes don't break existing functionality
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## Development Setup

1. Clone your fork of the repository
2. Run the setup script: `python3 setup.py`
3. Create a `.env` file based on `.env.example`
4. Install Ollama and the Gemma model if you plan to test content generation

## Coding Guidelines

- Follow PEP 8 style guidelines for Python code
- Write clear, descriptive commit messages
- Include comments and docstrings for new functions and classes
- Update documentation when changing functionality

## Testing

- Add tests for new features
- Ensure all tests pass before submitting a pull request
- Test your changes with different configurations

## Documentation

- Update the README.md file if your changes affect how users interact with the project
- Add comments to explain complex code sections
- Update any relevant documentation files

## Questions?

If you have any questions about contributing, please open an issue with your question.

Thank you for contributing to the EV Blog Automation Suite!
